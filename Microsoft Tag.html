<div class="_-codeSnippetContainer-1-2-23321 code-snippet" role="region" translate="no">&lt;script&gt;
    (function(w,d,t,r,u)
    {
        var f,n,i;
        w[u]=w[u]||[],f=function()
        {
            var o={ti:&quot;<!-- -->187206265<!-- -->&quot;, enableAutoSpaTracking: true};
            o.q=w[u],w[u]=new UET(o),w[u].push(&quot;pageLoad&quot;)
        },
        n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function()
        {
            var s=this.readyState;
            s&amp;&amp;s!==&quot;loaded&quot;&amp;&amp;s!==&quot;complete&quot;||(f(),n.onload=n.onreadystatechange=null)
        },
        i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)
    })
    (window,document,&quot;script&quot;,&quot;//bat.bing.com/bat.js&quot;,&quot;uetq&quot;);
&lt;/script&gt;</div>